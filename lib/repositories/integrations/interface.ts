import {
  Integration,
  IntegrationCreateInput,
  IntegrationUpdateInput,
  IntegrationQueryParams,
  IntegrationType,
  IntegrationStatus,
} from "@/lib/types/integrations"
import { BaseQueryParams } from "../BaseDBRepository"

// Database Repository Interface
export interface IntegrationDbQueryParams extends BaseQueryParams<Integration> {
  type?: IntegrationType
  status?: IntegrationStatus
  userId?: string
}

export interface IntegrationDBRepository {
  // Basic CRUD operations
  getById(id: string, includeDeleted?: boolean): Promise<Integration | null>
  getAll(params: IntegrationDbQueryParams): Promise<{ items: Integration[]; total: number }>
  getCount(params: IntegrationDbQueryParams): Promise<{ total: number }>
  create(data: IntegrationCreateInput & { userId: string }): Promise<Integration>
  update(id: string, data: IntegrationUpdateInput): Promise<Integration | null>
  delete(id: string, hardDelete?: boolean): Promise<boolean>
  restore(id: string): Promise<boolean>
  clear(): Promise<void>

  // Integration-specific operations
  findByUserAndType(userId: string, type: IntegrationType): Promise<Integration | null>
  findByUserId(userId: string): Promise<Integration[]>
  updateTokens(
    id: string,
    encryptedAccessToken: string,
    encryptedRefreshToken?: string,
    expiresAt?: Date
  ): Promise<Integration | null>
  updateStatus(id: string, status: IntegrationStatus, error?: string): Promise<Integration | null>
  findExpiredTokens(): Promise<Integration[]>
}

// Business Logic Interface
export interface IntegrationBusinessLogicInterface {
  // Basic CRUD operations
  getById(id: string, userId: string): Promise<Integration | null>
  getAll(params: IntegrationQueryParams, userId: string): Promise<{ items: Integration[]; total: number }>
  create(data: IntegrationCreateInput, userId: string): Promise<Integration>
  update(id: string, data: IntegrationUpdateInput, userId: string): Promise<Integration | null>
  delete(id: string, userId: string): Promise<boolean>

  // Integration-specific operations
  getUserIntegrations(userId: string): Promise<Integration[]>
  getIntegrationByType(userId: string, type: IntegrationType): Promise<Integration | null>
  
  // OAuth operations
  initiateOAuth(type: IntegrationType, userId: string, redirectUri?: string): Promise<{ authUrl: string; state: string }>
  handleOAuthCallback(
    code: string,
    state: string,
    type: IntegrationType,
    userId: string
  ): Promise<Integration>
  refreshToken(integrationId: string, userId: string): Promise<Integration>
  disconnect(integrationId: string, userId: string): Promise<boolean>
  
  // Token and status management
  validateTokens(integrationId: string, userId: string): Promise<boolean>
  updateConnectionStatus(integrationId: string, status: IntegrationStatus, error?: string): Promise<Integration | null>
  
  // Utility operations
  testConnection(integrationId: string, userId: string): Promise<{ success: boolean; message: string }>
  syncIntegration(integrationId: string, userId: string): Promise<{ success: boolean; syncedAt: Date }>
}
