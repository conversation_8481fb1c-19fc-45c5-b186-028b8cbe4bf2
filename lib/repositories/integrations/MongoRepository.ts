import {
  Integration,
  IntegrationCreateInput,
  IntegrationUpdateInput,
  IntegrationType,
  IntegrationStatus,
} from "@/lib/types/integrations"
import { IntegrationDBRepository, IntegrationDbQueryParams } from "./interface"
import { MongoDriver } from "../MongoDriver"
import { MONGO_COLLECTIONS } from "@/lib/db/mongoCollections"
import { buildMongoQuery } from "../queryBuilder"
import { generateId } from "@/lib/utils/common"

// Mapper function to convert MongoDB document to Integration
function mapMongoDocToIntegration(doc: any): Integration | null {
  if (!doc) return null
  const { _id, ...rest } = doc
  return {
    ...rest,
  } as Integration
}

// Search configuration for integrations
const integrationSearchConfig = {
  searchableFields: ["name", "description", "type"],
  filterableFields: ["type", "status", "userId"],
  sortableFields: ["name", "type", "status", "createdAt", "updatedAt"],
  buildMongoQuery: (_params: { sort: any[]; filters: any[] }) => ({
    query: {},
    sort: {},
  }),
}

export class MongoIntegrationRepository implements IntegrationDBRepository {
  private collection

  constructor(driver: MongoDriver) {
    this.collection = driver.getCollection(MONGO_COLLECTIONS.INTEGRATIONS)
  }

  async getById(id: string, includeDeleted = false): Promise<Integration | null> {
    const query: any = { id }
    if (!includeDeleted) {
      query.deletedAt = { $exists: false }
    }

    const doc = await this.collection.findOne(query)
    return mapMongoDocToIntegration(doc)
  }

  async getAll(params: IntegrationDbQueryParams): Promise<{ items: Integration[]; total: number }> {
    const initialQuery = integrationSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query, sort, limit, offset } = buildMongoQuery(
      initialQuery,
      {
        ...params,
        offset: params?.offset,
      },
      integrationSearchConfig.searchableFields,
    )

    // Apply user filter
    if (params.userId) {
      query.userId = params.userId
    }

    const [items, total] = await Promise.all([
      this.collection
        .find(query)
        .sort(sort)
        .skip(offset)
        .limit(limit)
        .toArray(),
      this.collection.countDocuments(query),
    ])

    return {
      items: items.map(mapMongoDocToIntegration).filter(Boolean) as Integration[],
      total,
    }
  }

  async getCount(params: IntegrationDbQueryParams): Promise<{ total: number }> {
    const initialQuery = integrationSearchConfig.buildMongoQuery({
      sort: params?.sort || [],
      filters: params?.filters || [],
    })

    const { query } = buildMongoQuery(
      initialQuery,
      params,
      integrationSearchConfig.searchableFields,
    )

    if (params.userId) {
      query.userId = params.userId
    }

    const total = await this.collection.countDocuments(query)
    return { total }
  }

  async create(data: IntegrationCreateInput & { userId: string }): Promise<Integration> {
    const now = new Date()
    const integration: Integration = {
      id: generateId("int"),
      userId: data.userId,
      type: data.type,
      name: data.name,
      description: data.description,
      status: "disconnected",
      createdAt: now,
      updatedAt: now,
    }

    await this.collection.insertOne(integration)
    return integration
  }

  async update(id: string, data: IntegrationUpdateInput): Promise<Integration | null> {
    const updateData = {
      ...data,
      updatedAt: new Date(),
    }

    const result = await this.collection.findOneAndUpdate(
      { id, deletedAt: { $exists: false } },
      { $set: updateData },
      { returnDocument: "after" }
    )

    return mapMongoDocToIntegration(result)
  }

  async delete(id: string, hardDelete = false): Promise<boolean> {
    if (hardDelete) {
      const result = await this.collection.deleteOne({ id })
      return result.deletedCount > 0
    } else {
      const result = await this.collection.updateOne(
        { id, deletedAt: { $exists: false } },
        { $set: { deletedAt: new Date() } }
      )
      return result.modifiedCount > 0
    }
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.collection.updateOne(
      { id },
      { $unset: { deletedAt: "" }, $set: { updatedAt: new Date() } }
    )
    return result.modifiedCount > 0
  }

  async clear(): Promise<void> {
    await this.collection.deleteMany({})
  }

  // Integration-specific operations
  async findByUserAndType(userId: string, type: IntegrationType): Promise<Integration | null> {
    const doc = await this.collection.findOne({
      userId,
      type,
      deletedAt: { $exists: false },
    })
    return mapMongoDocToIntegration(doc)
  }

  async findByUserId(userId: string): Promise<Integration[]> {
    const docs = await this.collection
      .find({
        userId,
        deletedAt: { $exists: false },
      })
      .sort({ createdAt: -1 })
      .toArray()

    return docs.map(mapMongoDocToIntegration).filter(Boolean) as Integration[]
  }

  async updateTokens(
    id: string,
    encryptedAccessToken: string,
    encryptedRefreshToken?: string,
    expiresAt?: Date
  ): Promise<Integration | null> {
    const updateData: any = {
      encryptedAccessToken,
      updatedAt: new Date(),
      connectedAt: new Date(),
      status: "connected" as IntegrationStatus,
      lastError: null,
      errorCount: 0,
    }

    if (encryptedRefreshToken) {
      updateData.encryptedRefreshToken = encryptedRefreshToken
    }

    if (expiresAt) {
      updateData.tokenExpiresAt = expiresAt
    }

    const result = await this.collection.findOneAndUpdate(
      { id, deletedAt: { $exists: false } },
      { $set: updateData },
      { returnDocument: "after" }
    )

    return mapMongoDocToIntegration(result)
  }

  async updateStatus(id: string, status: IntegrationStatus, error?: string): Promise<Integration | null> {
    const updateData: any = {
      status,
      updatedAt: new Date(),
    }

    if (error) {
      updateData.lastError = error
      updateData.$inc = { errorCount: 1 }
    } else if (status === "connected") {
      updateData.lastError = null
      updateData.errorCount = 0
    }

    const result = await this.collection.findOneAndUpdate(
      { id, deletedAt: { $exists: false } },
      { $set: updateData, ...(updateData.$inc && { $inc: updateData.$inc }) },
      { returnDocument: "after" }
    )

    return mapMongoDocToIntegration(result)
  }

  async findExpiredTokens(): Promise<Integration[]> {
    const now = new Date()
    const docs = await this.collection
      .find({
        tokenExpiresAt: { $lt: now },
        status: "connected",
        deletedAt: { $exists: false },
      })
      .toArray()

    return docs.map(mapMongoDocToIntegration).filter(Boolean) as Integration[]
  }
}
