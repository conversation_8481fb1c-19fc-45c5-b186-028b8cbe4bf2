import {
  Integration,
  IntegrationCreateInput,
  IntegrationUpdateInput,
  IntegrationQueryParams,
  IntegrationType,
  IntegrationStatus,
  GoogleOAuthTokens,
  GoogleUserInfo,
} from "@/lib/types/integrations"
import { IntegrationBusinessLogicInterface, IntegrationDBRepository } from "./interface"
import { encryptOAuthTokens, decryptOAuthTokens } from "@/lib/utils/encryption"
import { generateId, createError } from "@/lib/utils/common"
import axios from "axios"

const GOOGLE_OAUTH_CONFIG = {
  clientId: process.env.GOOGLE_OAUTH_CLIENT_ID!,
  clientSecret: process.env.GOOGLE_OAUTH_CLIENT_SECRET!,
  redirectUri: process.env.GOOGLE_OAUTH_REDIRECT_URI!,
  scopes: [
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/drive.readonly",
    "https://www.googleapis.com/auth/spreadsheets.readonly",
  ],
}

export class IntegrationBusinessLogic implements IntegrationBusinessLogicInterface {
  constructor(private dbRepo: IntegrationDBRepository) { }

  // Basic CRUD operations
  async getById(id: string, userId: string): Promise<Integration | null> {
    const integration = await this.dbRepo.getById(id)
    if (!integration || integration.userId !== userId) {
      return null
    }
    return integration
  }

  async getAll(params: IntegrationQueryParams, userId: string): Promise<{ items: Integration[]; total: number }> {
    const dbParams = {
      ...params,
      userId,
      limit: params.pageSize || 20,
      offset: params.page ? (params.page - 1) * (params.pageSize || 20) : 0,
    }
    return this.dbRepo.getAll(dbParams)
  }

  async create(data: IntegrationCreateInput, userId: string): Promise<Integration> {
    // Check if user already has this integration type
    const existing = await this.dbRepo.findByUserAndType(userId, data.type)
    if (existing) {
      throw createError(`Integration of type ${data.type} already exists`, "INTEGRATION_EXISTS")
    }

    return this.dbRepo.create({ ...data, userId })
  }

  async update(id: string, data: IntegrationUpdateInput, userId: string): Promise<Integration | null> {
    const integration = await this.getById(id, userId)
    if (!integration) {
      throw createError("Integration not found", "INTEGRATION_NOT_FOUND")
    }

    return this.dbRepo.update(id, data)
  }

  async delete(id: string, userId: string): Promise<boolean> {
    const integration = await this.getById(id, userId)
    if (!integration) {
      throw createError("Integration not found", "INTEGRATION_NOT_FOUND")
    }

    return this.dbRepo.delete(id)
  }

  // Integration-specific operations
  async getUserIntegrations(userId: string): Promise<Integration[]> {
    return this.dbRepo.findByUserId(userId)
  }

  async getIntegrationByType(userId: string, type: IntegrationType): Promise<Integration | null> {
    return this.dbRepo.findByUserAndType(userId, type)
  }

  // OAuth operations
  async initiateOAuth(type: IntegrationType, userId: string, redirectUri?: string): Promise<{ authUrl: string; state: string }> {
    if (type !== "google") {
      throw createError("Unsupported integration type", "UNSUPPORTED_INTEGRATION")
    }

    // Generate state parameter for security
    const state = generateId("oauth")

    // Store state in session or temporary storage (you might want to use Redis for this)
    // For now, we'll include userId in the state for validation
    const stateData = `${state}:${userId}`

    const authUrl = new URL("https://accounts.google.com/o/oauth2/v2/auth")
    authUrl.searchParams.set("client_id", GOOGLE_OAUTH_CONFIG.clientId)
    authUrl.searchParams.set("redirect_uri", redirectUri || GOOGLE_OAUTH_CONFIG.redirectUri)
    authUrl.searchParams.set("response_type", "code")
    authUrl.searchParams.set("scope", GOOGLE_OAUTH_CONFIG.scopes.join(" "))
    authUrl.searchParams.set("state", stateData)
    authUrl.searchParams.set("access_type", "offline")
    authUrl.searchParams.set("prompt", "consent")

    console.log("authUrl", authUrl.toString())

    return {
      authUrl: authUrl.toString(),
      state: stateData,
    }
  }

  async handleOAuthCallback(
    code: string,
    state: string,
    type: IntegrationType,
    userId: string
  ): Promise<Integration> {
    if (type !== "google") {
      throw createError("Unsupported integration type", "UNSUPPORTED_INTEGRATION")
    }

    // Validate state parameter
    const [stateId, stateUserId] = state.split(":")
    if (stateUserId !== userId) {
      throw createError("Invalid state parameter", "INVALID_STATE")
    }

    try {
      // Exchange code for tokens
      const tokenResponse = await axios.post("https://oauth2.googleapis.com/token", {
        client_id: GOOGLE_OAUTH_CONFIG.clientId,
        client_secret: GOOGLE_OAUTH_CONFIG.clientSecret,
        code,
        grant_type: "authorization_code",
        redirect_uri: GOOGLE_OAUTH_CONFIG.redirectUri,
      })

      const tokens: GoogleOAuthTokens = tokenResponse.data

      // Get user info from Google
      const userInfoResponse = await axios.get("https://www.googleapis.com/oauth2/v2/userinfo", {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      })

      const userInfo: GoogleUserInfo = userInfoResponse.data

      // Encrypt tokens
      const encryptedTokens = encryptOAuthTokens({
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
      })

      // Find or create integration
      let integration = await this.dbRepo.findByUserAndType(userId, type)

      if (!integration) {
        integration = await this.dbRepo.create({
          type,
          name: `Google - ${userInfo.email}`,
          description: `Connected Google account for ${userInfo.name}`,
          userId,
        })
      }

      // Update with tokens and metadata
      const expiresAt = new Date(Date.now() + tokens.expires_in * 1000)

      integration = await this.dbRepo.updateTokens(
        integration.id,
        encryptedTokens.encryptedAccessToken,
        encryptedTokens.encryptedRefreshToken,
        expiresAt
      )

      if (!integration) {
        throw createError("Failed to update integration tokens", "TOKEN_UPDATE_FAILED")
      }

      // Update metadata
      integration = await this.dbRepo.update(integration.id, {
        metadata: {
          userInfo,
          scopes: GOOGLE_OAUTH_CONFIG.scopes,
          grantedPermissions: tokens.scope?.split(" ") || [],
        },
      })

      return integration!
    } catch (error: any) {
      console.error("OAuth callback error:", error)
      throw createError("Failed to complete OAuth flow", "OAUTH_FAILED")
    }
  }

  async refreshToken(integrationId: string, userId: string): Promise<Integration> {
    const integration = await this.getById(integrationId, userId)
    if (!integration) {
      throw createError("Integration not found", "INTEGRATION_NOT_FOUND")
    }

    if (!integration.encryptedRefreshToken) {
      throw createError("No refresh token available", "NO_REFRESH_TOKEN")
    }

    try {
      const tokens = decryptOAuthTokens({
        encryptedAccessToken: integration.encryptedAccessToken!,
        encryptedRefreshToken: integration.encryptedRefreshToken,
      })

      // Refresh the access token
      const tokenResponse = await axios.post("https://oauth2.googleapis.com/token", {
        client_id: GOOGLE_OAUTH_CONFIG.clientId,
        client_secret: GOOGLE_OAUTH_CONFIG.clientSecret,
        refresh_token: tokens.refresh_token,
        grant_type: "refresh_token",
      })

      const newTokens: GoogleOAuthTokens = tokenResponse.data

      // Encrypt new tokens
      const encryptedTokens = encryptOAuthTokens({
        access_token: newTokens.access_token,
        refresh_token: newTokens.refresh_token || tokens.refresh_token, // Keep old refresh token if new one not provided
      })

      const expiresAt = new Date(Date.now() + newTokens.expires_in * 1000)

      const updatedIntegration = await this.dbRepo.updateTokens(
        integrationId,
        encryptedTokens.encryptedAccessToken,
        encryptedTokens.encryptedRefreshToken,
        expiresAt
      )

      if (!updatedIntegration) {
        throw createError("Failed to update tokens", "TOKEN_UPDATE_FAILED")
      }

      return updatedIntegration
    } catch (error: any) {
      console.error("Token refresh error:", error)
      await this.dbRepo.updateStatus(integrationId, "error", "Failed to refresh token")
      throw createError("Failed to refresh token", "TOKEN_REFRESH_FAILED")
    }
  }

  async disconnect(integrationId: string, userId: string): Promise<boolean> {
    const integration = await this.getById(integrationId, userId)
    if (!integration) {
      throw createError("Integration not found", "INTEGRATION_NOT_FOUND")
    }

    // Update status to disconnected and clear tokens
    await this.dbRepo.update(integrationId, {
      status: "disconnected",
    })

    // Clear encrypted tokens
    await this.dbRepo.updateTokens(integrationId, "", undefined, undefined)

    return true
  }

  async validateTokens(integrationId: string, userId: string): Promise<boolean> {
    const integration = await this.getById(integrationId, userId)
    if (!integration || !integration.encryptedAccessToken) {
      return false
    }

    try {
      const tokens = decryptOAuthTokens({
        encryptedAccessToken: integration.encryptedAccessToken,
        encryptedRefreshToken: integration.encryptedRefreshToken,
      })

      // Test the token by making a simple API call
      const response = await axios.get("https://www.googleapis.com/oauth2/v2/userinfo", {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      })

      if (response.status === 200) {
        await this.dbRepo.updateStatus(integrationId, "connected")
        return true
      }

      return false
    } catch (error) {
      // Token might be expired, try to refresh
      if (integration.encryptedRefreshToken) {
        try {
          await this.refreshToken(integrationId, userId)
          return true
        } catch (refreshError) {
          await this.dbRepo.updateStatus(integrationId, "error", "Token validation failed")
          return false
        }
      }

      await this.dbRepo.updateStatus(integrationId, "error", "Token validation failed")
      return false
    }
  }

  async updateConnectionStatus(integrationId: string, status: IntegrationStatus, error?: string): Promise<Integration | null> {
    return this.dbRepo.updateStatus(integrationId, status, error)
  }

  async testConnection(integrationId: string, userId: string): Promise<{ success: boolean; message: string }> {
    const isValid = await this.validateTokens(integrationId, userId)
    return {
      success: isValid,
      message: isValid ? "Connection is working" : "Connection failed",
    }
  }

  async syncIntegration(integrationId: string, userId: string): Promise<{ success: boolean; syncedAt: Date }> {
    const integration = await this.getById(integrationId, userId)
    if (!integration) {
      throw createError("Integration not found", "INTEGRATION_NOT_FOUND")
    }

    // For now, just update the lastSyncAt timestamp
    // In a real implementation, you would sync data from the external service
    await this.dbRepo.update(integrationId, {
      lastSyncAt: new Date(),
    })

    return {
      success: true,
      syncedAt: new Date(),
    }
  }
}
