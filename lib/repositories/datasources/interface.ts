import { SessionContext } from "../auth/types"

// Tool call parameter definition for strongly typed tool parameters
export interface ToolParameter {
  name: string
  type: "string" | "number" | "boolean" | "object" | "array"
  description: string
  required: boolean
  enum?: string[] // For string types with predefined values
  properties?: Record<string, ToolParameter> // For object types
  items?: ToolParameter // For array types
  value?: string // Direct value input or LLM variable name
}

// LLM parameter definition for parameters that LLM will generate
export interface LLMParameter {
  name: string
  type: "string" | "number" | "boolean" | "object" | "array"
  description: string
  prompt?: string // Prompt instruction for LLM to generate this parameter
}

// Tool call configuration for TOOL type datasources
export interface ToolCallConfig {
  toolId: string
  description: string
  parameters: ToolParameter[]
  llmParameters?: LLMParameter[] // Parameters that LLM will generate
}

export interface Datasource {
  id: string
  name: string
  type: "TEXT" | "TOOL"
  content?: string // For TEXT type datasources
  isActive?: boolean
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  organizationId?: string
  // Relationship field to track source knowledge base entry
  sourceKnowledgeBaseId?: string // ID of the knowledge base entry that generated this datasource
  // Tool call configuration for TOOL type
  toolCallConfig?: ToolCallConfig
  // Query for matching in vector DB (for TOOL type)
  query?: string
}

export interface DatasourceCreateInput {
  name: string
  type: string
  content?: string // For TEXT type datasources
  isActive?: boolean
  sourceKnowledgeBaseId?: string // ID of the knowledge base entry that generated this datasource
  parseId?: string
  // Tool call configuration for TOOL type
  toolCallConfig?: ToolCallConfig
  // Query for matching in vector DB (for TOOL type)
  query?: string
}

export interface DatasourceUpdateInput {
  name?: string
  type?: string
  url?: string
  content?: string // For TEXT type datasources
  isActive?: boolean
  updatedBy?: string
  organizationId?: string
  // Tool call configuration for TOOL type
  toolCallConfig?: ToolCallConfig
  // Query for matching in vector DB (for TOOL type)
  query?: string
}

export type DatasourceQueryParams = {
  search?: string
  limit?: number
  offset?: number
  sort?: { field: keyof Datasource | string; direction: "ASC" | "DESC" }[]
  filters?: {
    field: keyof Datasource | string
    value: Datasource[keyof Datasource] | any
  }[]
  includeDeleted?: boolean
}

export interface DatasourceBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<Datasource | null>
  getAll(
    params: DatasourceQueryParams,
    context: SessionContext,
  ): Promise<{
    items: Datasource[]
    total: number
  }>
  create(
    data: DatasourceCreateInput,
    context: SessionContext,
  ): Promise<Datasource>
  update(
    id: string,
    data: DatasourceUpdateInput,
    context: SessionContext,
  ): Promise<Datasource | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>

  searchMatchingQuery(
    query: string,
    context: SessionContext,
    limit: number,
  ): Promise<Datasource[]>
}
