{"name": "Pembaca PostgreSQL", "description": "Membaca data dari database PostgreSQL", "parameters": {"tableName": "<PERSON><PERSON> tabel PostgreSQL untuk query", "column": "Nama kolom untuk dipilih (dip<PERSON>h<PERSON> koma). Gunakan * untuk semua kolom. Contoh: id, name, email", "whereClause": "<PERSON><PERSON><PERSON> klausa WHERE. Gunakan {{llmParam.variableName}} untuk variabel LLM. Contoh: status = '{{llmParam.orderStatus}}' AND created_at > '2024-01-01'", "limit": "<PERSON><PERSON><PERSON> maksimum baris yang dike<PERSON> (default: 10)"}}