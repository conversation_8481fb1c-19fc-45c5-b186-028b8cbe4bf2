{"name": "PostgreSQL Reader", "description": "Read data from PostgreSQL database", "parameters": {"tableName": "Name of the PostgreSQL table to query", "column": "Column names to select (comma-separated). Use * for all columns. Example: id, name, email", "whereClause": "WHERE clause condition. Use {{llmParam.variableName}} for LLM variables. Example: status = '{{llmParam.orderStatus}}' AND created_at > '2024-01-01'", "limit": "Maximum number of rows to return (default: 10)"}}