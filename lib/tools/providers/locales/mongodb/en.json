{"name": "MongoDB Reader", "description": "Read data from MongoDB collection", "parameters": {"collectionName": "Name of the MongoDB collection to query", "queryRaw": "Raw MongoDB query in JSON format. Use {{llmParam.variableName}} for LLM variables. Example: {\"status\": \"{{llmParam.orderStatus}}\", \"date\": {\"$gte\": \"2024-01-01\"}}", "limit": "Maximum number of documents to return (default: 10)"}}