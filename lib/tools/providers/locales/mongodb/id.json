{"name": "Pembaca MongoDB", "description": "Membaca data dari koleksi MongoDB", "parameters": {"collectionName": "Nama koleksi MongoDB untuk query", "queryRaw": "Query MongoDB mentah dalam format JSON. Gunakan {{llmParam.variableName}} untuk variabel LLM. Contoh: {\"status\": \"{{llmParam.orderStatus}}\", \"date\": {\"$gte\": \"2024-01-01\"}}", "limit": "<PERSON><PERSON><PERSON> maksimum dokumen yang dike<PERSON> (default: 10)"}}