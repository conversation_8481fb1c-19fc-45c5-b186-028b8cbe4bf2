{"name": "Pemanggil API", "description": "Melakukan panggilan HTTP API ke layanan eksternal", "parameters": {"endpoint": "URL endpoint API untuk dipanggil. Gunakan {{llmParam.variableName}} untuk variabel LLM. Contoh: https://api.example.com/users/{{llmParam.userId}}", "method": "Metode HTTP - hanya GET yang didukung", "params": "Parameter query sebagai objek flat dalam format JSON. Gunakan {{llmParam.variableName}} untuk variabel LLM. Contoh: {\"status\": \"{{llmParam.status}}\", \"limit\": \"10\"}"}}