{"name": "API Caller", "description": "Make HTTP API calls to external services", "parameters": {"endpoint": "The API endpoint URL to call. Use {{llmParam.variableName}} for LLM variables. Example: https://api.example.com/users/{{llmParam.userId}}", "method": "HTTP method - only GET is supported", "params": "Query parameters as flat object in JSON format. Use {{llmParam.variableName}} for LLM variables. Example: {\"status\": \"{{llmParam.status}}\", \"limit\": \"10\"}"}}