{"name": "مستدعي API", "description": "إجراء استدعاءات HTTP API للخدمات الخارجية", "parameters": {"endpoint": "رابط نقطة النهاية API للاستدعاء. استخدم {{llmParam.variableName}} لمتغيرات LLM. مثال: https://api.example.com/users/{{llmParam.userId}}", "method": "طريقة HTTP - GET فقط مدعومة", "params": "معاملات الاستعلام كائن مسطح بتنسيق JSON. استخدم {{llmParam.variableName}} لمتغيرات LLM. مثال: {\"status\": \"{{llmParam.status}}\", \"limit\": \"10\"}"}}