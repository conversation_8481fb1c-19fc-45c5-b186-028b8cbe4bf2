import { localize } from '@/localization/functions/server'
import { ToolProvider } from '../interfaces'
import { locales } from './locales/mongodb'

export async function MongoDBTool(): Promise<ToolProvider> {
  const id = 'mongodb_reader'
  const { t } = await localize("mongodb-tool", locales)
  return {
    id,
    name: t("name"),
    description: t("description"),
    parameters: [
      {
        name: 'collectionName',
        type: 'string',
        required: true,
        description: t("parameters.collectionName")
      },
      {
        name: 'queryRaw',
        type: 'string',
        required: true,
        description: t("parameters.queryRaw")
      },
      {
        name: 'limit',
        type: 'number',
        required: false,
        description: t("parameters.limit"),
        defaultValue: 10
      }
    ],

    async execute(params: Record<string, any>): Promise<any> {
      const { collectionName, queryRaw, limit = 10 } = params

      // Validate parameters
      if (!collectionName || typeof collectionName !== 'string') {
        throw new Error('collectionName is required and must be a string')
      }
      if (!queryRaw || typeof queryRaw !== 'string') {
        throw new Error('queryRaw is required and must be a string')
      }
      if (limit && (typeof limit !== 'number' || limit <= 0)) {
        throw new Error('limit must be a positive number')
      }

      try {
        // TODO: Implement actual MongoDB connection and query
        // For now, return mock data that simulates real MongoDB response

        // Simulate database query delay
        await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 250))

        // Parse the raw query (in production, this would be used with MongoDB driver)
        let parsedQuery: any = {}
        try {
          parsedQuery = JSON.parse(queryRaw)
        } catch (e) {
          throw new Error('Invalid JSON in queryRaw parameter')
        }

        // Mock response structure similar to MongoDB query results
        const mockDocuments = []
        const actualLimit = Math.min(limit, 5) // Limit mock data

        for (let i = 0; i < actualLimit; i++) {
          const doc: any = {
            _id: `507f1f77bcf86cd79943901${i}`,
            status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)],
            orderId: `ORD-${1000 + i}`,
            customerId: `CUST-${100 + i}`,
            amount: Math.floor(Math.random() * 1000) + 100,
            createdAt: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString(),
            updatedAt: new Date().toISOString(),
            metadata: {
              source: 'mongodb_mock',
              index: i,
              queryUsed: parsedQuery
            }
          }

          mockDocuments.push(doc)
        }

        return {
          collection: collectionName,
          query: parsedQuery,
          limit,
          totalFound: mockDocuments.length,
          documents: mockDocuments,
          metadata: {
            source: 'MongoDB',
            timestamp: new Date().toISOString(),
            executionStats: {
              totalDocsExamined: mockDocuments.length,
              totalDocsReturned: mockDocuments.length,
              executionTimeMillis: Math.floor(Math.random() * 50) + 10
            },
            note: 'This is a mock response. In production, this would connect to actual MongoDB.'
          }
        }

      } catch (error: any) {
        throw new Error(`MongoDB execution failed: ${error.message}`)
      }
    }
  }
}
