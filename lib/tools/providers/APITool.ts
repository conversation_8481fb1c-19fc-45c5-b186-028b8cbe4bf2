import { localize } from "@/localization/functions/server"
import { <PERSON>lProvider } from "../interfaces"
import { locales } from "./locales/api"

export async function APITool(): Promise<ToolProvider> {
  const id = "api_caller"
  const { t } = await localize("api-tool", locales)
  return {
    id,
    name: t("name"),
    description: t("description"),
    parameters: [
      {
        name: "endpoint",
        type: "string",
        required: true,
        description: t("parameters.endpoint"),
      },
      {
        name: "method",
        type: "string",
        required: true,
        description: t("parameters.method"),
        enum: ["GET"],
        defaultValue: "GET",
      },
      {
        name: "params",
        type: "object",
        required: false,
        description: t("parameters.params"),
      },
      {
        name: "headers",
        type: "object",
        required: false,
        description: t("parameters.headers"),
      },
    ],

    async execute(params: Record<string, any>): Promise<any> {
      const { endpoint, method = "GET", params: requestParams } = params

      // Validate parameters
      if (!endpoint || typeof endpoint !== "string") {
        throw new Error("endpoint is required and must be a string")
      }
      if (!method || typeof method !== "string") {
        throw new Error("method is required and must be a string")
      }
      if (method.toUpperCase() !== "GET") {
        throw new Error("Only GET method is supported")
      }

      try {
        // Validate URL format
        new URL(endpoint)
      } catch {
        throw new Error("endpoint must be a valid URL")
      }

      try {
        // TODO: Implement actual HTTP request using fetch or axios
        // For now, return mock data that simulates real API response

        // Parse query parameters if provided
        let parsedParams: any = {}
        if (requestParams && typeof requestParams === "string") {
          try {
            parsedParams = JSON.parse(requestParams)
          } catch (e) {
            throw new Error("Invalid JSON in params parameter")
          }
        }

        // Simulate network delay
        await new Promise((resolve) =>
          setTimeout(resolve, 200 + Math.random() * 800),
        )

        const requestMethod = method.toUpperCase()

        // Mock different responses based on method
        let mockResponse: any = {
          status: "success",
          message: `${requestMethod} request to ${endpoint} completed successfully`,
        }

        switch (requestMethod) {
          case "GET":
            mockResponse.data = {
              id: Math.floor(Math.random() * 1000),
              name: "Sample Resource",
              description: "This is a mock GET response",
              timestamp: new Date().toISOString(),
              params_received: parsedParams || {},
            }
            break
        }

        return {
          request: {
            endpoint,
            method: requestMethod,
            params: parsedParams,
          },
          response: {
            status: 200,
            statusText: "OK",
            data: mockResponse,
            headers: {
              "content-type": "application/json",
              "x-response-time": `${Math.floor(Math.random() * 100) + 50}ms`,
            },
          },
          metadata: {
            source: "HTTP API",
            timestamp: new Date().toISOString(),
            executionTime: Math.floor(Math.random() * 500) + 100,
            note: "This is a mock response. In production, this would make actual HTTP requests.",
          },
        }
      } catch (error: any) {
        throw new Error(`API call failed: ${error.message}`)
      }
    },
  }
}
