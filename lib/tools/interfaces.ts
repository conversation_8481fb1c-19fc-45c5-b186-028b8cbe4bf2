export interface ToolParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  required: boolean
  description?: string
  defaultValue?: any
  enum?: string[] // For string types with predefined values
  properties?: Record<string, ToolParameter> // For object types
  items?: ToolParameter // For array types
}

export interface ToolProvider {
  id: string
  name: string
  description: string
  parameters: ToolParameter[]
  execute(params: Record<string, any>): Promise<any>
}

export interface ToolExecutionResult {
  success: boolean
  data?: any
  error?: string
  executionTime: number
}
