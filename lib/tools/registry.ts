import { Too<PERSON><PERSON>rovider } from './interfaces'
import { GoogleSheetTool } from './providers/GoogleSheetTool'
import { MongoDBTool } from './providers/MongoDBTool'
import { PostgreSQLTool } from './providers/PostgreSQLTool'
import { APITool } from './providers/APITool'

// Tool registry - maps tool IDs to their async factory implementations
export const TOOL_REGISTRY: Record<string, () => Promise<ToolProvider>> = {
  'google_sheets_reader': GoogleSheetTool,
  'mongodb_reader': MongoDBTool,
  'postgresql_reader': PostgreSQLTool,
  'api_caller': APITool,
}

// Get a tool by ID
export async function getToolById(toolId: string): Promise<ToolProvider | null> {
  const toolFactory = TOOL_REGISTRY[toolId]
  return toolFactory ? toolFactory() : null
}

// Get all tools
export async function getAllTools(): Promise<ToolProvider[]> {
  const factories = Object.values(TOOL_REGISTRY)
  return Promise.all(factories.map(factory => factory()))
}

// Get tool IDs
export function getToolIds(): string[] {
  return Object.keys(TOOL_REGISTRY)
}

// Check if tool exists
export function toolExists(toolId: string): boolean {
  return toolId in TOOL_REGISTRY
}
