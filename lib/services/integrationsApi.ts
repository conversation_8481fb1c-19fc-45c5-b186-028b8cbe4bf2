import { BaseAPI } from "./baseApi"
import {
  Integration,
  IntegrationCreateInput,
  IntegrationUpdateInput,
  IntegrationQueryParams,
  IntegrationType,
  OAuthInitiateResponse,
  OAuthCallbackRequest,
  IntegrationTestResponse,
} from "@/lib/types/integrations"

export class IntegrationsAPI extends BaseAPI {
  // Basic CRUD operations
  static GetAll = (params?: IntegrationQueryParams) => {
    const searchParams = new URLSearchParams()
    
    if (params?.page) searchParams.set("page", params.page.toString())
    if (params?.pageSize) searchParams.set("pageSize", params.pageSize.toString())
    if (params?.search) searchParams.set("search", params.search)
    if (params?.type) searchParams.set("type", params.type)
    if (params?.status) searchParams.set("status", params.status)

    const queryString = searchParams.toString()
    const url = queryString ? `/integrations?${queryString}` : "/integrations"
    
    return new BaseAPI(url).build<{ items: Integration[]; total: number }>()
  }

  static GetById = (id: string) =>
    new BaseAPI(`/integrations/${id}`).build<Integration>()

  static Create = (data: IntegrationCreateInput) =>
    new BaseAPI("/integrations", data, "POST").build<Integration>()

  static Update = (id: string, data: IntegrationUpdateInput) =>
    new BaseAPI(`/integrations/${id}`, data, "PUT").build<Integration>()

  static Delete = (id: string) =>
    new BaseAPI(`/integrations/${id}`, undefined, "DELETE").build<{ success: boolean }>()

  // OAuth operations
  static InitiateOAuth = (data: { type: IntegrationType; redirectUri?: string }) =>
    new BaseAPI("/integrations/oauth/initiate", data, "POST").build<OAuthInitiateResponse>()

  static HandleOAuthCallback = (data: OAuthCallbackRequest) =>
    new BaseAPI("/integrations/oauth/callback", data, "POST").build<Integration>()

  // Integration management
  static Disconnect = (id: string) =>
    new BaseAPI(`/integrations/${id}/disconnect`, {}, "POST").build<{ success: boolean }>()

  static TestConnection = (id: string) =>
    new BaseAPI(`/integrations/${id}/test`, {}, "POST").build<IntegrationTestResponse>()

  // Convenience methods for specific integration types
  static ConnectGoogle = (redirectUri?: string) =>
    IntegrationsAPI.InitiateOAuth({ type: "google", redirectUri })

  static GetGoogleIntegration = () =>
    IntegrationsAPI.GetAll({ type: "google", pageSize: 1 })

  // Helper method to get user's integrations by type
  static GetByType = (type: IntegrationType) =>
    IntegrationsAPI.GetAll({ type, pageSize: 1 })

  // Helper method to get connected integrations only
  static GetConnected = () =>
    IntegrationsAPI.GetAll({ status: "connected" })

  // Helper method to get disconnected integrations only
  static GetDisconnected = () =>
    IntegrationsAPI.GetAll({ status: "disconnected" })
}
