import { promises as fs } from 'fs'
import path from 'path'
import { loadBusinessConfig, BusinessConfig } from '@/lib/config/business-config'

export interface MarkdownPageConfig {
  title: string
  content: string
  lastUpdated?: string
}

/**
 * Load markdown content for a page, checking business config first, then falling back to file
 */
export async function loadMarkdownContent(
  pageName: 'privacy-policy' | 'terms' | 'data-deletion',
  locale: string
): Promise<MarkdownPageConfig> {
  try {
    // First try to load from business config if it has the content
    const businessConfig = await loadBusinessConfig(locale)
    const configContent = getContentFromBusinessConfig(businessConfig, pageName)

    if (configContent) {
      return configContent
    }
  } catch (error) {
    console.log(`Failed to load ${pageName} from business config, trying file fallback`)
  }

  try {
    const localizedFilePath = path.join(process.cwd(), 'app', '(public)', pageName, 'content', `${locale}.md`)
    let content: string

    try {
      content = await fs.readFile(localizedFilePath, 'utf-8')
    } catch (localeError) {
      // Fallback to English if specific locale not found
      const englishFilePath = path.join(process.cwd(), 'app', '(public)', pageName, 'locales', 'en.md')
      content = await fs.readFile(englishFilePath, 'utf-8')
      console.log(`Locale ${locale} not found for ${pageName}, using English fallback`)
    }

    // Extract title from first h1 if present, otherwise use default
    const titleMatch = content.match(/^#\s+(.+)$/m)
    const title = titleMatch ? titleMatch[1] : getDefaultTitle(pageName)

    return {
      title,
      content,
      lastUpdated: new Date().toISOString()
    }
  } catch (error) {
    console.log(`Failed to load ${pageName} from localized files, using default content`)
    return getDefaultContent(pageName, locale)
  }
}

/**
 * Extract content from business config if available
 */
function getContentFromBusinessConfig(
  businessConfig: BusinessConfig,
  pageName: string
): MarkdownPageConfig | null {
  // Check if business config has been extended with legal content
  const extendedConfig = businessConfig as any

  if (extendedConfig.legal && extendedConfig.legal[pageName]) {
    const legalContent = extendedConfig.legal[pageName]
    return {
      title: legalContent.title || getDefaultTitle(pageName),
      content: legalContent.content || getDefaultContent(pageName).content,
      lastUpdated: legalContent.lastUpdated || new Date().toISOString()
    }
  }

  return null
}

/**
 * Get default title for a page
 */
function getDefaultTitle(pageName: string): string {
  switch (pageName) {
    case 'privacy-policy':
      return 'Privacy Policy'
    case 'terms':
      return 'Terms of Service'
    case 'data-deletion':
      return 'Data Deletion Policy'
    default:
      return 'Legal Document'
  }
}

/**
 * Get default content when no file or config is available
 */
function getDefaultContent(pageName: string, locale: string = 'en'): MarkdownPageConfig {
  const title = getDefaultTitle(pageName)

  const defaultContents = {
    'privacy-policy': `# Privacy Policy

## Information We Collect

We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support.

## How We Use Your Information

We use the information we collect to:
- Provide, maintain, and improve our services
- Process transactions and send related information
- Send technical notices and support messages
- Respond to your comments and questions

## Information Sharing

We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.

## Data Security

We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

## Contact Us

If you have any questions about this Privacy Policy, please contact us at the information provided on our website.

*Last updated: ${new Date().toLocaleDateString()}*`,

    'terms': `# Terms of Service

## Acceptance of Terms

By accessing and using this service, you accept and agree to be bound by the terms and provision of this agreement.

## Use License

Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only.

## Disclaimer

The materials on our website are provided on an 'as is' basis. We make no warranties, expressed or implied, and hereby disclaim and negate all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.

## Limitations

In no event shall our company or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on our website.

## Accuracy of Materials

The materials appearing on our website could include technical, typographical, or photographic errors. We do not warrant that any of the materials on its website are accurate, complete, or current.

## Contact Information

If you have any questions about these Terms of Service, please contact us at the information provided on our website.

*Last updated: ${new Date().toLocaleDateString()}*`,

    'data-deletion': `# Data Deletion Policy

## Your Right to Delete Data

You have the right to request deletion of your personal data that we have collected and stored.

## How to Request Data Deletion

To request deletion of your data:
1. Contact us using the information provided on our website
2. Provide verification of your identity
3. Specify what data you want deleted

## What Data Can Be Deleted

You can request deletion of:
- Your account information
- Personal data we have collected
- Usage data and analytics
- Communication history

## Data Retention

Some data may be retained for legal or business purposes as required by law, including:
- Transaction records for accounting purposes
- Data required for ongoing legal obligations
- Anonymized data for analytics

## Processing Time

We will process your deletion request within 30 days of verification of your identity and request.

## Confirmation

You will receive confirmation once your data has been deleted from our systems.

## Contact Us

To request data deletion or if you have questions about this policy, please contact us at the information provided on our website.

*Last updated: ${new Date().toLocaleDateString()}*`
  }

  return {
    title,
    content: defaultContents[pageName as keyof typeof defaultContents] || `# ${title}\n\nContent not available.`,
    lastUpdated: new Date().toISOString()
  }
}
