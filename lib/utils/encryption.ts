import crypto from "crypto"

// Encryption configuration
const ALGORITHM = "aes-256-gcm"
const KEY_LENGTH = 32 // 256 bits
const IV_LENGTH = 16 // 128 bits
const TAG_LENGTH = 16 // 128 bits

// Get encryption key from environment variable
function getEncryptionKey(): Buffer {
  const key = process.env.ENCRYPTION_KEY
  if (!key) {
    throw new Error("ENCRYPTION_KEY environment variable is required")
  }
  
  // If key is shorter than required, derive it using PBKDF2
  if (key.length < KEY_LENGTH * 2) { // hex string should be 64 chars for 32 bytes
    const salt = process.env.ENCRYPTION_SALT || "default-salt-change-in-production"
    return crypto.pbkdf2Sync(key, salt, 100000, KEY_LENGTH, "sha256")
  }
  
  // If key is provided as hex string
  return Buffer.from(key, "hex")
}

/**
 * Encrypts a string using AES-256-GCM
 * @param text - The text to encrypt
 * @returns Encrypted data as base64 string (format: iv:tag:encrypted)
 */
export function encrypt(text: string): string {
  try {
    const key = getEncryptionKey()
    const iv = crypto.randomBytes(IV_LENGTH)
    
    const cipher = crypto.createCipher(ALGORITHM, key)
    cipher.setAAD(Buffer.from("integration-token", "utf8"))
    
    let encrypted = cipher.update(text, "utf8", "hex")
    encrypted += cipher.final("hex")
    
    const tag = cipher.getAuthTag()
    
    // Combine iv, tag, and encrypted data
    const combined = `${iv.toString("hex")}:${tag.toString("hex")}:${encrypted}`
    return Buffer.from(combined).toString("base64")
  } catch (error) {
    console.error("Encryption error:", error)
    throw new Error("Failed to encrypt data")
  }
}

/**
 * Decrypts a string that was encrypted with the encrypt function
 * @param encryptedData - The encrypted data as base64 string
 * @returns Decrypted text
 */
export function decrypt(encryptedData: string): string {
  try {
    const key = getEncryptionKey()
    
    // Decode from base64 and split components
    const combined = Buffer.from(encryptedData, "base64").toString("utf8")
    const [ivHex, tagHex, encrypted] = combined.split(":")
    
    if (!ivHex || !tagHex || !encrypted) {
      throw new Error("Invalid encrypted data format")
    }
    
    const iv = Buffer.from(ivHex, "hex")
    const tag = Buffer.from(tagHex, "hex")
    
    const decipher = crypto.createDecipher(ALGORITHM, key)
    decipher.setAAD(Buffer.from("integration-token", "utf8"))
    decipher.setAuthTag(tag)
    
    let decrypted = decipher.update(encrypted, "hex", "utf8")
    decrypted += decipher.final("utf8")
    
    return decrypted
  } catch (error) {
    console.error("Decryption error:", error)
    throw new Error("Failed to decrypt data")
  }
}

/**
 * Encrypts OAuth tokens for secure storage
 * @param tokens - OAuth tokens object
 * @returns Object with encrypted tokens
 */
export function encryptOAuthTokens(tokens: {
  access_token: string
  refresh_token?: string
}): {
  encryptedAccessToken: string
  encryptedRefreshToken?: string
} {
  const result: any = {
    encryptedAccessToken: encrypt(tokens.access_token),
  }
  
  if (tokens.refresh_token) {
    result.encryptedRefreshToken = encrypt(tokens.refresh_token)
  }
  
  return result
}

/**
 * Decrypts OAuth tokens from storage
 * @param encryptedTokens - Encrypted tokens object
 * @returns Object with decrypted tokens
 */
export function decryptOAuthTokens(encryptedTokens: {
  encryptedAccessToken: string
  encryptedRefreshToken?: string
}): {
  access_token: string
  refresh_token?: string
} {
  const result: any = {
    access_token: decrypt(encryptedTokens.encryptedAccessToken),
  }
  
  if (encryptedTokens.encryptedRefreshToken) {
    result.refresh_token = decrypt(encryptedTokens.encryptedRefreshToken)
  }
  
  return result
}

/**
 * Generates a secure random encryption key
 * @returns Hex string of random key
 */
export function generateEncryptionKey(): string {
  return crypto.randomBytes(KEY_LENGTH).toString("hex")
}

/**
 * Validates if the encryption configuration is properly set up
 * @returns True if encryption is properly configured
 */
export function validateEncryptionSetup(): boolean {
  try {
    const testData = "test-encryption-data"
    const encrypted = encrypt(testData)
    const decrypted = decrypt(encrypted)
    return decrypted === testData
  } catch (error) {
    console.error("Encryption setup validation failed:", error)
    return false
  }
}
