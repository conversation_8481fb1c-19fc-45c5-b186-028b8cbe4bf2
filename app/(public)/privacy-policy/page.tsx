// app/privacy-policy/page.tsx

import { cookies } from 'next/headers'
import { loadMarkdownContent } from '@/lib/utils/markdown-loader'
import { loadBusinessConfig } from '@/lib/config/business-config'
import { kDEFAULT_LANG } from '@/app/constant'
import { LegalPageTemplate } from '@/components/LegalPageTemplate'

interface Props {
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>
}

export async function generateMetadata() {
  const cookieStore = await cookies()
  const locale = cookieStore.get('locale')?.value || kDEFAULT_LANG
  const content = await loadMarkdownContent('privacy-policy', locale)

  return {
    title: content.title,
  }
}

export default async function PrivacyPolicyPage({ searchParams }: Props) {
  const resolvedParams = await searchParams
  const localeFromQuery = resolvedParams?.lang

  const cookieStore = await cookies()
  const localeFromCookie = cookieStore.get('locale')?.value
  const locale = (localeFromQuery as string) || localeFromCookie || kDEFAULT_LANG

  const [content, businessConfig] = await Promise.all([
    loadMarkdownContent('privacy-policy', locale),
    loadBusinessConfig(locale),
  ])

  return (
    <LegalPageTemplate
      title={content.title}
      lastUpdated={content.lastUpdated}
      businessName={businessConfig.businessInfo.name}
      contactEmail={businessConfig.contact.email}
      contactPhone={businessConfig.contact.phone}
      contactAddress={businessConfig.contact.address}
      businessHours={businessConfig.businessHours.weekdays.start + ' - ' + businessConfig.businessHours.weekdays.end}
      content={content.content}
    />
  )
}
