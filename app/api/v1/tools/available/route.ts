import { NextRequest, NextResponse } from "next/server"
import { getAllTools } from "@/lib/tools/registry"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function GET(request: NextRequest) {
  try {
    const tools = await getAllTools()

    // Transform tools to include only necessary information for the frontend
    const availableTools = tools.map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters.map(param => ({
        name: param.name,
        type: param.type,
        required: param.required,
        description: param.description,
        defaultValue: param.defaultValue
      }))
    }))

    return NextResponse.json(
      new ResponseWrapper("success", availableTools),
      { status: 200 }
    )
  } catch (error: any) {
    console.error("Get available tools error:", error)

    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        null,
        ["Failed to get available tools"],
        ["INTERNAL_SERVER_ERROR"]
      ),
      { status: 500 }
    )
  }
}
