import { NextRequest, NextResponse } from "next/server"
import { getAllTools } from "@/lib/tools/registry"
import { ResponseWrapper } from "@/lib/types/responseWrapper"

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params
    const tools = await getAllTools()
    const tool = tools.find((t) => t.id === id)

    if (!tool) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          null,
          [`Tool with id "${id}" not found.`],
          ["TOOL_NOT_FOUND"],
        ),
        { status: 404 },
      )
    }

    const transformedTool = {
      id: tool.id,
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters.map((param) => ({
        name: param.name,
        type: param.type,
        required: param.required,
        description: param.description,
        defaultValue: param.defaultValue,
      })),
    }

    return NextResponse.json(new ResponseWrapper("success", transformedTool), {
      status: 200,
    })
  } catch (error: any) {
    console.error("Get tool by ID error:", error)

    return NextResponse.json(
      new ResponseWrapper(
        "failed",
        null,
        ["Failed to retrieve tool details"],
        ["INTERNAL_SERVER_ERROR"],
      ),
      { status: 500 },
    )
  }
}
