import { NextRequest, NextResponse } from "next/server"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleTestDatasourceTool } from "./impl"
import { ERROR_CODES } from "@/app/api/error_codes"
import { buildSessionContext } from "../../../../sharedFunction"

// POST /api/v1/datasources/[id]/test - Test datasource tool with parameters
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const body = await req.json()
    const { datasourcesBusinessLogic } = getBusinessLogics()
    const result = await implHandleTestDatasourceTool(
      id,
      body,
      datasourcesBusinessLogic,
      context
    )

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Datasource test error:", error)
    return NextResponse.json(
      { 
        status: "failed", 
        errors: ["Internal server error"],
        errorCodes: [ERROR_CODES.INTERNAL_SERVER_ERROR]
      },
      { status: 500 }
    )
  }
}
