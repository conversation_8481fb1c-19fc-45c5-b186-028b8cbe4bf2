import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleOAuthInitiate } from "../../impl"

// POST /api/v1/integrations/oauth/initiate - Initiate OAuth flow
export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    const body = await req.json()

    const result = await implHandleOAuthInitiate(body, context, integrationBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("OAuth initiate error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}
