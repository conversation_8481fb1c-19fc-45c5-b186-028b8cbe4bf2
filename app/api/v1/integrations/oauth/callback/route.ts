import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleOAuthCallback } from "../../impl"

// POST /api/v1/integrations/oauth/callback - Handle OAuth callback
export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    const body = await req.json()

    const result = await implHandleOAuthCallback(body, context, integrationBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("OAuth callback error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url)
    const code = url.searchParams.get("code")
    const state = url.searchParams.get("state")
    const error = url.searchParams.get("error")

    // Handle OAuth errors
    if (error) {
      const errorDescription = url.searchParams.get("error_description") || "OAuth authorization failed"
      return NextResponse.redirect(
        new URL(process.env.NEXT_PUBLIC_APP_BASE_URL! + `/settings/integrations?error=${encodeURIComponent(errorDescription)}`, req.url)
      )
    }

    if (!code || !state) {
      return NextResponse.redirect(
        new URL(process.env.NEXT_PUBLIC_APP_BASE_URL! + "/settings/integrations?error=Missing authorization code or state", req.url)
      )
    }

    // Extract user ID from state (format: stateId:userId)
    const [, userId] = state.split(":")
    if (!userId) {
      return NextResponse.redirect(
        new URL(process.env.NEXT_PUBLIC_APP_BASE_URL! + "/settings/integrations?error=Invalid state parameter", req.url)
      )
    }

    // For now, redirect to frontend with the code and state
    // The frontend will then make a POST request to complete the OAuth flow
    const redirectUrl = new URL(process.env.NEXT_PUBLIC_APP_BASE_URL! + "/settings/integrations", req.url)
    redirectUrl.searchParams.set("code", code)
    redirectUrl.searchParams.set("state", state)
    redirectUrl.searchParams.set("type", "google") // Assuming Google for now

    return NextResponse.redirect(redirectUrl)
  } catch (error: any) {
    console.error("OAuth callback GET error:", error)
    return NextResponse.redirect(
      new URL(process.env.NEXT_PUBLIC_APP_BASE_URL! + "/settings/integrations?error=OAuth callback failed", req.url)
    )
  }
}
