import { z } from "zod"
import { ResponseWrapper } from "@/lib/types/responseWrapper"
import { ERROR_CODES } from "@/app/api/error_codes"
import { SessionContext } from "@/lib/repositories/auth/types"
import {
  Integration,
  IntegrationQueryParams,
} from "@/lib/types/integrations"
import { IntegrationBusinessLogicInterface } from "@/lib/repositories/integrations/interface"

// Validation schemas
const IntegrationCreateSchema = z.object({
  type: z.enum(["google", "microsoft", "slack", "discord"]),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
})

const IntegrationUpdateSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  status: z.enum(["connected", "disconnected", "error", "pending"]).optional(),
})

const OAuthInitiateSchema = z.object({
  type: z.enum(["google", "microsoft", "slack", "discord"]),
  redirectUri: z.string().url().optional(),
})

const OAuthCallbackSchema = z.object({
  code: z.string().min(1, "Authorization code is required"),
  state: z.string().min(1, "State parameter is required"),
  type: z.enum(["google", "microsoft", "slack", "discord"]),
})

// Implementation functions
export async function implHandleGetAllIntegrations(
  params: IntegrationQueryParams,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<{ items: Integration[]; total: number } | null>
}> {
  try {
    const result = await businessLogic.getAll(params, context.user.id)

    return {
      status: 200,
      body: new ResponseWrapper("success", result),
    }
  } catch (error: any) {
    console.error("Get all integrations error:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to fetch integrations"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}

export async function implHandleGetIntegration(
  id: string,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<Integration | null>
}> {
  try {
    const integration = await businessLogic.getById(id, context.user.id)

    if (!integration) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Integration not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", integration),
    }
  } catch (error: any) {
    console.error("Get integration error:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to fetch integration"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}

export async function implHandleCreateIntegration(
  body: any,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<Integration | null>
}> {
  try {
    const validationResult = IntegrationCreateSchema.safeParse(body)

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", null, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const integration = await businessLogic.create(validationResult.data, context.user.id)

    return {
      status: 201,
      body: new ResponseWrapper("success", integration),
    }
  } catch (error: any) {
    console.error("Create integration error:", error)

    if (error.code === "INTEGRATION_EXISTS") {
      return {
        status: 409,
        body: new ResponseWrapper(
          "failed",
          null,
          [error.message],
          [ERROR_CODES.DUPLICATE_RESOURCE]
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to create integration"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}

export async function implHandleUpdateIntegration(
  id: string,
  body: any,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<Integration | null>
}> {
  try {
    const validationResult = IntegrationUpdateSchema.safeParse(body)

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", null, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const integration = await businessLogic.update(id, validationResult.data, context.user.id)

    if (!integration) {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          null,
          ["Integration not found"],
          [ERROR_CODES.NOT_FOUND]
        ),
      }
    }

    return {
      status: 200,
      body: new ResponseWrapper("success", integration),
    }
  } catch (error: any) {
    console.error("Update integration error:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to update integration"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}

export async function implHandleDeleteIntegration(
  id: string,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<{ success: boolean } | null>
}> {
  try {
    const success = await businessLogic.delete(id, context.user.id)

    return {
      status: 200,
      body: new ResponseWrapper("success", { success }),
    }
  } catch (error: any) {
    console.error("Delete integration error:", error)

    if (error.code === "INTEGRATION_NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          null,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to delete integration"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}

// OAuth specific implementations
export async function implHandleOAuthInitiate(
  body: any,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<{ authUrl: string; state: string } | null>
}> {
  try {
    const validationResult = OAuthInitiateSchema.safeParse(body)

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", null, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const { type, redirectUri } = validationResult.data
    const result = await businessLogic.initiateOAuth(type, context.user.id, redirectUri)

    return {
      status: 200,
      body: new ResponseWrapper("success", result),
    }
  } catch (error: any) {
    console.error("OAuth initiate error:", error)
    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to initiate OAuth flow"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}

export async function implHandleOAuthCallback(
  body: any,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<Integration | null>
}> {
  try {
    const validationResult = OAuthCallbackSchema.safeParse(body)

    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => err.message)
      return {
        status: 400,
        body: new ResponseWrapper("failed", null, errors, [
          ERROR_CODES.VALIDATION_FAILED,
        ]),
      }
    }

    const { code, state, type } = validationResult.data
    const integration = await businessLogic.handleOAuthCallback(code, state, type, context.user.id)

    return {
      status: 200,
      body: new ResponseWrapper("success", integration),
    }
  } catch (error: any) {
    console.error("OAuth callback error:", error)

    if (error.code === "INVALID_STATE") {
      return {
        status: 400,
        body: new ResponseWrapper(
          "failed",
          null,
          [error.message],
          [ERROR_CODES.VALIDATION_FAILED]
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to complete OAuth flow"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}

export async function implHandleDisconnectIntegration(
  id: string,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<{ success: boolean } | null>
}> {
  try {
    const success = await businessLogic.disconnect(id, context.user.id)

    return {
      status: 200,
      body: new ResponseWrapper("success", { success }),
    }
  } catch (error: any) {
    console.error("Disconnect integration error:", error)

    if (error.code === "INTEGRATION_NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          null,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to disconnect integration"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}

export async function implHandleTestConnection(
  id: string,
  context: SessionContext,
  businessLogic: IntegrationBusinessLogicInterface
): Promise<{
  status: number
  body: ResponseWrapper<{ success: boolean; message: string } | null>
}> {
  try {
    const result = await businessLogic.testConnection(id, context.user.id)

    return {
      status: 200,
      body: new ResponseWrapper("success", result),
    }
  } catch (error: any) {
    console.error("Test connection error:", error)

    if (error.code === "INTEGRATION_NOT_FOUND") {
      return {
        status: 404,
        body: new ResponseWrapper(
          "failed",
          null,
          [error.message],
          [ERROR_CODES.NOT_FOUND]
        ),
      }
    }

    return {
      status: 500,
      body: new ResponseWrapper(
        "failed",
        null,
        ["Failed to test connection"],
        [ERROR_CODES.INTERNAL_SERVER_ERROR]
      ),
    }
  }
}
