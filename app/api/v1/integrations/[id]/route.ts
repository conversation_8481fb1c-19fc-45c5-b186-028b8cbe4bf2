import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetIntegration,
  implHandleUpdateIntegration,
  implHandleDeleteIntegration,
} from "../impl"

// GET /api/v1/integrations/[id] - Get a specific integration
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    const result = await implHandleGetIntegration(id, context, integrationBusinessLogic)

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Integration GET error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}

// PUT /api/v1/integrations/[id] - Update a specific integration
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    const body = await req.json()

    const result = await implHandleUpdateIntegration(id, body, context, integrationBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Integration PUT error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}

// DELETE /api/v1/integrations/[id] - Delete a specific integration
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    const result = await implHandleDeleteIntegration(id, context, integrationBusinessLogic)

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Integration DELETE error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}
