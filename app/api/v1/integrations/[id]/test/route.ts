import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleTestConnection } from "../../impl"

// POST /api/v1/integrations/[id]/test - Test integration connection
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    const result = await implHandleTestConnection(id, context, integrationBusinessLogic)

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Integration test error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}
