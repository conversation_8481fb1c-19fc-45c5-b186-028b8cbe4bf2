import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import { implHandleDisconnectIntegration } from "../../impl"

// POST /api/v1/integrations/[id]/disconnect - Disconnect an integration
export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    const result = await implHandleDisconnectIntegration(id, context, integrationBusinessLogic)

    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Integration disconnect error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}
