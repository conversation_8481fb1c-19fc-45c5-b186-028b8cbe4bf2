import { NextRequest, NextResponse } from "next/server"
import { buildSessionContext } from "@/app/api/sharedFunction"
import { getBusinessLogics } from "@/lib/repositories/businessLogics"
import {
  implHandleGetAllIntegrations,
  implHandleCreateIntegration,
} from "./impl"

// GET /api/v1/integrations - Get all integrations for the user
export async function GET(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    
    // Parse query parameters
    const url = new URL(req.url)
    const params = {
      page: url.searchParams.get("page") ? parseInt(url.searchParams.get("page")!) : undefined,
      pageSize: url.searchParams.get("pageSize") ? parseInt(url.searchParams.get("pageSize")!) : undefined,
      search: url.searchParams.get("search") || undefined,
      type: url.searchParams.get("type") as any || undefined,
      status: url.searchParams.get("status") as any || undefined,
    }

    const result = await implHandleGetAllIntegrations(params, context, integrationBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Integrations GET error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}

// POST /api/v1/integrations - Create a new integration
export async function POST(req: NextRequest) {
  try {
    const { context, response } = await buildSessionContext(req)
    if (response) {
      return response
    }

    const { integrationBusinessLogic } = getBusinessLogics()
    const body = await req.json()

    const result = await implHandleCreateIntegration(body, context, integrationBusinessLogic)
    return NextResponse.json(result.body, { status: result.status })
  } catch (error: any) {
    console.error("Integrations POST error:", error)
    return NextResponse.json(
      { status: "failed", errors: ["Internal server error"] },
      { status: 500 }
    )
  }
}
