{"name": "admin-management", "version": "1.0.0", "description": "System Prompts Management for CS AI - Backoffice system for managing AI prompts", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:init": "node scripts/init-db.js", "db:reset": "node scripts/init-db.js", "test:api": "node test-api.js"}, "devDependencies": {"@sveltejs/adapter-node": "^2.0.0", "@sveltejs/kit": "^1.20.4", "@sveltejs/vite-plugin-svelte": "^2.4.2", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "svelte": "^4.0.5", "svelte-check": "^3.4.3", "tailwindcss": "^3.3.0", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^4.4.2"}, "dependencies": {"dotenv": "^17.2.1", "lucide-svelte": "^0.541.0", "mongodb": "^6.18.0", "pino": "^9.10.0", "pino-pretty": "^13.1.1"}}