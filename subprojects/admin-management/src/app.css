@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: "Inter", system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased h-screen overflow-hidden;
  }
}

@layer components {
  /* Chat Container */
  .chat-container {
    @apply flex h-screen bg-white shadow-lg rounded-lg overflow-hidden;
  }

  /* Conversations List */
  .conversations-panel {
    @apply w-80 bg-gray-50 border-r border-gray-200 flex flex-col;
  }

  .conversations-header {
    @apply p-4 bg-white border-b border-gray-200 flex items-center justify-between;
  }

  .conversations-title {
    @apply text-lg font-semibold text-gray-900;
  }

  .new-conversation-btn {
    @apply bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200;
  }

  .conversations-list {
    @apply flex-1 overflow-y-auto;
  }

  .conversation-item {
    @apply p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-100 transition-colors duration-150;
  }

  .conversation-item.active {
    @apply bg-blue-50 border-blue-200 border-r-4 border-r-blue-500;
  }

  .conversation-name {
    @apply font-medium text-gray-900 truncate mb-1;
  }

  .conversation-from {
    @apply text-xs text-gray-500 mb-0.5;
  }

  .conversation-preview {
    @apply text-sm text-gray-600 truncate;
  }

  .conversation-time {
    @apply text-xs text-gray-400 mt-1;
  }

  /* Chat Panel */
  .chat-panel {
    @apply flex-1 flex flex-col bg-white;
  }

  .chat-header {
    @apply p-4 bg-gray-50 border-b border-gray-200;
  }

  .chat-title {
    @apply text-lg font-semibold text-gray-900 mb-1 cursor-pointer hover:text-blue-600 transition-colors;
  }

  .chat-from {
    @apply text-sm text-gray-600;
  }

  .chat-info {
    @apply mt-2 space-y-1;
  }

  .chat-field {
    @apply flex items-center space-x-2;
  }

  .field-label {
    @apply text-sm font-medium text-gray-500 w-12;
  }

  .field-value {
    @apply text-sm text-gray-700 cursor-pointer hover:text-blue-600 transition-colors;
  }

  .field-input {
    @apply text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  .field-btn {
    @apply text-xs px-2 py-1 rounded transition-colors duration-200;
  }

  .field-btn.edit {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-600;
  }

  .field-btn.save {
    @apply bg-green-100 hover:bg-green-200 text-green-700;
  }

  .field-btn.cancel {
    @apply bg-red-100 hover:bg-red-200 text-red-700;
  }

  .title-input {
    @apply text-lg font-semibold border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  .title-btn {
    @apply text-sm px-2 py-1 rounded ml-2 transition-colors duration-200;
  }

  .title-btn.edit {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-600;
  }

  .title-btn.save {
    @apply bg-green-100 hover:bg-green-200 text-green-700;
  }

  .title-btn.cancel {
    @apply bg-red-100 hover:bg-red-200 text-red-700;
  }

  /* Messages */
  .chat-messages {
    @apply flex-1 overflow-y-auto p-4 space-y-3 bg-gray-50;
  }

  .message-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl shadow-sm break-words;
  }

  .message-bubble.sent {
    @apply bg-blue-600 text-white ml-auto rounded-br-md;
  }

  .message-bubble.received {
    @apply bg-white text-gray-900 mr-auto border border-gray-200 rounded-bl-md;
  }

  .message-text {
    @apply text-sm leading-relaxed;
  }

  .message-time {
    @apply text-xs mt-1 opacity-70;
  }

  /* Chat Input */
  .chat-input-container {
    @apply p-4 bg-white border-t border-gray-200;
  }

  .chat-input-form {
    @apply flex space-x-3;
  }

  .chat-input {
    @apply flex-1 border border-gray-300 rounded-full px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm;
  }

  .send-button {
    @apply bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-full font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Dialog */
  .dialog-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
  }

  .dialog {
    @apply bg-white rounded-lg shadow-xl max-w-md w-full mx-4;
  }

  .dialog-header {
    @apply flex items-center justify-between p-4 border-b border-gray-200;
  }

  .dialog-title {
    @apply text-lg font-semibold text-gray-900;
  }

  .dialog-close {
    @apply text-gray-400 hover:text-gray-600 text-xl font-bold cursor-pointer;
  }

  .dialog-body {
    @apply p-4 space-y-4;
  }

  .dialog-field {
    @apply space-y-2;
  }

  .dialog-label {
    @apply block text-sm font-medium text-gray-700;
  }

  .dialog-input {
    @apply w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  .dialog-footer {
    @apply flex justify-end space-x-3 p-4 border-t border-gray-200;
  }

  .dialog-btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .dialog-btn.primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white;
  }

  .dialog-btn.secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-700;
  }

  /* Empty State */
  .empty-state {
    @apply flex flex-col items-center justify-center h-full text-gray-500 text-center;
  }

  .empty-state h3 {
    @apply mb-2 text-gray-600 text-lg font-medium;
  }

  /* Edit Mode */
  .edit-name-input {
    @apply bg-transparent border-none text-sm font-medium text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500 rounded px-1;
  }

  .edit-buttons {
    @apply flex space-x-1 ml-2;
  }

  .edit-btn {
    @apply text-xs px-1.5 py-0.5 rounded transition-colors duration-200;
  }

  .edit-btn.save {
    @apply bg-green-100 hover:bg-green-200 text-green-700;
  }

  .edit-btn.cancel {
    @apply bg-red-100 hover:bg-red-200 text-red-700;
  }
}

@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
