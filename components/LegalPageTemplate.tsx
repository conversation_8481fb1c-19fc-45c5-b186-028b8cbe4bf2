"use client"

import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'
import { MarkdownRenderer } from './markdown-renderer'
import { useEffect, useMemo, useState } from 'react'
import { LanguageSelector } from './ui/language-selector'

interface Props {
    title: string
    lastUpdated?: string
    businessName: string
    contactEmail: string
    contactPhone: string
    contactAddress: string
    businessHours: string
    content: string
}

export function LegalPageTemplate({ title, lastUpdated, businessName, contactEmail, contactPhone, contactAddress, businessHours, content }: Props) {
    const finalContact = useMemo(() => {
        return content.replaceAll(/\{\{businessEmail\}\}/g, contactEmail)
            .replaceAll(/\{\{businessPhone\}\}/g, contactPhone)
            .replaceAll(/\{\{businessAddress\}\}/g, contactAddress)
            .replaceAll(/\{\{businessHours\}\}/g, businessHours)
    }, [contactEmail, contactPhone, contactAddress])

    const [lastUpdatedAt, setLastUpdatedAt] = useState<string>("")
    useEffect(() => {
        if (lastUpdated) {
            setLastUpdatedAt(new Date(lastUpdated).toLocaleDateString())
        }
    }, [lastUpdated])

    async function reloadPage() {
        window.location.reload()
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="container mx-auto px-4 py-8 max-w-4xl">
                <div className="mb-8">
                    <div className='flex justify-between'>
                        <Link
                            href="/"
                            className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 mb-4"
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Home
                        </Link>
                        <LanguageSelector onChangeLocale={(locale) => reloadPage()} />
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                                {title}
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400">
                                {businessName}
                            </p>
                        </div>

                        {lastUpdated && (
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                                Last updated: {lastUpdatedAt}
                            </div>
                        )}
                    </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8">
                    <MarkdownRenderer content={finalContact} />
                </div>

                <div className="mt-8 text-center text-gray-600 dark:text-gray-400">
                    <p>
                        If you have any questions, please{' '}
                        <a
                            href={`mailto:${contactEmail}`}
                            className="text-blue-600 dark:text-blue-400 hover:underline"
                        >
                            contact us
                        </a>
                        .
                    </p>
                </div>
            </div>
        </div>
    )
}
