"use client"

import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { DatasourceFormData } from "./DatasourceFormBase"

interface TextDatasourceFieldsProps {
  formData: DatasourceFormData
  onFormDataChange: (data: DatasourceFormData) => void
}

export default function TextDatasourceFields({
  formData,
  onFormDataChange,
}: TextDatasourceFieldsProps) {
  const { t } = useLocalization("datasource", locales)

  const updateFormData = (updates: Partial<DatasourceFormData>) => {
    onFormDataChange({ ...formData, ...updates })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("form.section.content_title")}</CardTitle>
        <CardDescription>
          Enter the text content for this datasource
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="content">
            {t("form.fields.content.label")}
          </Label>
          <Textarea
            id="content"
            value={formData.content || ""}
            onChange={(e) => updateFormData({ content: e.target.value })}
            placeholder="Enter your text content here..."
            className="min-h-40"
            maxLength={10000}
            required
          />
          <div className="flex justify-between text-sm text-gray-500">
            <span>{(formData.content || "").length}/10,000 characters</span>
            <span
              className={
                (formData.content || "").length > 9000 ? "text-orange-500" : ""
              }
            >
              {(formData.content || "").length > 9000 && "Approaching limit"}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function validateTextDatasource(formData: DatasourceFormData): boolean {
  return !!(
    formData.name.trim() &&
    formData.content?.trim()
  )
}
