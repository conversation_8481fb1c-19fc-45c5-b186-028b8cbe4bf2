"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Play, Clock, CheckCircle, XCircle, Loader2 } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { ToolParameter } from "@/lib/repositories/datasources/interface"
import {
  DatasourcesAPI,
  TestDatasourceToolResponse,
} from "@/lib/services/datasourcesApi"
import { DatasourceFormData } from "./DatasourceFormBase"

interface TestDatasourceDialogProps {
  open: boolean
  formData: DatasourceFormData
  onOpenChange: (open: boolean) => void
  datasourceId: string
  toolId: string
  description: string
  parameters: ToolParameter[]
}

export default function TestDatasourceDialog({
  open,
  formData,
  onOpenChange,
  datasourceId,
  toolId,
  description,
  parameters,
}: TestDatasourceDialogProps) {
  const { t } = useLocalization("datasource", locales)

  const [parameterValues, setParameterValues] = useState<Record<string, any>>(
    {},
  )
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] =
    useState<TestDatasourceToolResponse | null>(null)

  // Auto-populate parameter values from formData when dialog opens
  useEffect(() => {
    if (open && formData.toolCallConfig?.parameters) {
      const initialValues: Record<string, any> = {}

      formData.toolCallConfig.parameters.forEach((param) => {
        // Only populate if parameter has a direct value (not LLM-filled)
        if (param.value && !isLLMParameter(param.value)) {
          // Try to parse the value based on parameter type
          try {
            switch (param.type) {
              case "number":
                initialValues[param.name] = parseFloat(param.value) || 0
                break
              case "boolean":
                initialValues[param.name] = param.value.toLowerCase() === "true"
                break
              case "object":
              case "array":
                initialValues[param.name] = JSON.parse(param.value)
                break
              default: // string
                initialValues[param.name] = param.value
                break
            }
          } catch {
            // If parsing fails, use as string
            initialValues[param.name] = param.value
          }
        }
      })

      setParameterValues(initialValues)
    }
  }, [open, formData.toolCallConfig?.parameters])

  // Helper function to check if a value is an LLM parameter reference
  const isLLMParameter = (value: string) => {
    return (formData.toolCallConfig?.llmParameters || []).some(
      (llmParam) => llmParam.name === value,
    )
  }

  const handleParameterChange = (paramName: string, value: any) => {
    setParameterValues((prev) => ({
      ...prev,
      [paramName]: value,
    }))
  }

  const handleTest = async () => {
    setIsLoading(true)
    setTestResult(null)

    try {
      const response = await DatasourcesAPI.TestTool(datasourceId, {
        parameters: parameterValues,
      }).request()
      setTestResult(response)
    } catch (error: any) {
      setTestResult({
        success: false,
        error: error.message,
        toolId,
        parametersUsed: parameterValues,
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setParameterValues({})
    setTestResult(null)
    onOpenChange(false)
  }

  const renderParameterInput = (param: ToolParameter) => {
    const value = parameterValues[param.name] || ""

    // Handle enum parameters with select field
    if (param.enum && param.enum.length > 0) {
      return (
        <Select
          value={value}
          onValueChange={(selectedValue) =>
            handleParameterChange(param.name, selectedValue)
          }
        >
          <SelectTrigger>
            <SelectValue placeholder={`Select ${param.name}`} />
          </SelectTrigger>
          <SelectContent>
            {param.enum.map((enumValue) => (
              <SelectItem key={enumValue} value={enumValue}>
                {enumValue}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )
    }

    switch (param.type) {
      case "boolean":
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={param.name}
              checked={value === true}
              onChange={(e) =>
                handleParameterChange(param.name, e.target.checked)
              }
            />
            <Label htmlFor={param.name}>{param.name}</Label>
          </div>
        )

      case "number":
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) =>
              handleParameterChange(param.name, parseFloat(e.target.value) || 0)
            }
            placeholder={`Enter ${param.name}`}
          />
        )

      case "array":
      case "object":
        return (
          <Textarea
            value={
              typeof value === "string" ? value : JSON.stringify(value, null, 2)
            }
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value)
                handleParameterChange(param.name, parsed)
              } catch {
                handleParameterChange(param.name, e.target.value)
              }
            }}
            placeholder={`Enter ${param.type} as JSON`}
            className="min-h-20 font-mono text-sm"
          />
        )

      default: // string
        return (
          <Input
            type="text"
            value={value}
            onChange={(e) => handleParameterChange(param.name, e.target.value)}
            placeholder={`Enter ${param.name}`}
          />
        )
    }
  }

  const canTest = parameters
    .filter((p) => p.required)
    .every(
      (p) =>
        parameterValues[p.name] !== undefined && parameterValues[p.name] !== "",
    )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            {t("test.dialog.title")}
          </DialogTitle>
          <DialogDescription>
            {t("test.dialog.description")}
            <br />
            <span className="text-green-600 text-sm">
              Parameter values are automatically populated from your form
              configuration.
            </span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Tool Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{toolId}</CardTitle>
              <CardDescription>{description}</CardDescription>
            </CardHeader>
          </Card>

          {/* Parameters */}
          <Card>
            <CardHeader>
              <CardTitle>{t("test.parameters.title")}</CardTitle>
              <CardDescription>
                {t("test.parameters.description")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {parameters.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  {t("test.parameters.no_parameters")}
                </p>
              ) : (
                parameters.map((param) => {
                  const hasAutoValue =
                    parameterValues[param.name] !== undefined &&
                    parameterValues[param.name] !== ""
                  const isLLMFilled = param.value && isLLMParameter(param.value)

                  return (
                    <div key={param.name} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Label htmlFor={param.name} className="font-medium">
                          {param.name}
                        </Label>
                        <Badge
                          variant={param.required ? "destructive" : "secondary"}
                        >
                          {param.type}
                        </Badge>
                        {param.required && (
                          <Badge variant="outline" className="text-xs">
                            {t("test.parameters.required")}
                          </Badge>
                        )}
                        {param.enum && (
                          <Badge
                            variant="outline"
                            className="text-xs bg-purple-50 text-purple-700"
                          >
                            Enum
                          </Badge>
                        )}
                        {hasAutoValue && !isLLMFilled && (
                          <Badge
                            variant="outline"
                            className="text-xs bg-green-50 text-green-700"
                          >
                            Auto-filled
                          </Badge>
                        )}
                        {isLLMFilled && (
                          <Badge
                            variant="outline"
                            className="text-xs bg-blue-50 text-blue-700"
                          >
                            LLM Parameter
                          </Badge>
                        )}
                      </div>
                      {param.description && (
                        <p className="text-sm text-gray-600">
                          {param.description}
                        </p>
                      )}
                      {param.enum && (
                        <p className="text-xs text-purple-600">
                          Available values: {param.enum.join(", ")}
                        </p>
                      )}
                      {isLLMFilled ? (
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                          <p className="text-sm text-blue-800">
                            This parameter is filled by LLM variable:{" "}
                            <strong>{param.value}</strong>
                          </p>
                          <p className="text-xs text-blue-600 mt-1">
                            Cannot be manually set for testing. LLM will provide
                            the value during actual execution.
                          </p>
                        </div>
                      ) : (
                        renderParameterInput(param)
                      )}
                    </div>
                  )
                })
              )}
            </CardContent>
          </Card>

          {/* Test Results */}
          {testResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {testResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  {t("test.results.title")}
                  {testResult.executionTime && (
                    <Badge variant="outline" className="ml-auto">
                      <Clock className="h-3 w-3 mr-1" />
                      {testResult.executionTime}ms
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {testResult.success ? (
                  <div className="space-y-3">
                    <div className="text-sm text-green-600 font-medium">
                      {t("test.results.success")}
                    </div>
                    <Separator />
                    <div>
                      <Label className="text-sm font-medium">
                        {t("test.results.response")}:
                      </Label>
                      <pre className="mt-2 p-3 bg-gray-50 rounded-md text-sm overflow-x-auto">
                        {JSON.stringify(testResult.result, null, 2)}
                      </pre>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="text-sm text-red-600 font-medium">
                      {t("test.results.error")}
                    </div>
                    <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="text-sm text-red-700">{testResult.error}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {t("test.dialog.close")}
          </Button>
          <Button
            onClick={handleTest}
            disabled={!canTest || isLoading}
            className="flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                {t("test.dialog.testing")}
              </>
            ) : (
              <>
                <Play className="h-4 w-4" />
                {t("test.dialog.test")}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
