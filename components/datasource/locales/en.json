{"form": {"buttons": {"cancel": "Cancel", "save": "Save", "saving": "Saving...", "try": "Try"}, "common": {"approaching_limit": "Approaching limit", "characters": "characters"}, "fields": {"access_key": {"label": "Access Key (Optional)", "placeholder": "Enter access key if required"}, "content": {"label": "Content", "placeholder": "Enter your text content here..."}, "name": {"label": "Name", "placeholder": "Enter datasource name"}, "type": {"api": "API Endpoint", "database": "Database", "label": "Type", "placeholder": "Select type", "text": "Text Content"}, "url": {"label": "URL", "placeholder": "https://example.com/api"}}, "section": {"connection_desc": "Enter the connection details for this datasource", "connection_title": "Connection Details", "content_desc": "Enter the text content for this datasource", "content_title": "Content", "details_desc": "Enter the basic information for your datasource", "details_title": "Datasource Details"}, "validation": {"content_required": "Please enter content for TEXT type datasource", "name_required": "Please enter a name", "save_failed": "Failed to save datasource. Please try again.", "url_required": "Please enter a URL for non-TEXT type datasource"}, "tool": {"title": "Tool Configuration", "description": "Description", "query_label": "Query for Matching", "query_placeholder": "Enter the query that will be used to match this tool in vector database...", "tool_name_label": "Tool Name", "tool_name_placeholder": "Select a prebuilt tool or choose custom", "tool_name_custom_placeholder": "Enter custom tool name (e.g., get_weather, search_database)", "tool_description_label": "Tool Description", "tool_description_placeholder": "Describe what this tool does and when to use it...", "parameters_label": "Tool Parameters", "add_parameter": "Add Parameter", "add_custom_parameter": "Add Custom Parameter", "parameter_title": "Parameter", "parameter_name_label": "Parameter Name", "parameter_name_placeholder": "e.g., location, query", "parameter_type_label": "Parameter Type", "parameter_required_label": "Required", "parameter_description_label": "Description", "parameter_description_placeholder": "Describe this parameter...", "no_parameters": "No parameters defined. Click \"Add Parameter\" to add tool parameters.", "loading_tools": "Loading tools...", "prebuilt_tool_selected": "Prebuilt Tool Selected: Parameters have been automatically configured based on the tool requirements. You can modify them below if needed.", "auto_populated_description": "(Auto-populated from prebuilt tool)", "auto_configured_parameters": "(Auto-configured from prebuilt tool)", "parameters_auto_configured": "Parameters Auto-Configured: The parameters below are automatically set based on the selected prebuilt tool. You can still modify them if needed, or add additional parameters using the \"Add Parameter\" button.", "custom_tool_option": "Custom Tool", "custom_tool_description": "Define your own tool configuration", "tool_parameters_tab": "Tool Parameters", "llm_parameters_tab": "LLM Parameters", "tool_parameters_description": "Configure the parameters that will be passed directly to the tool execution.", "llm_parameters_description": "Define parameters that the LLM will generate dynamically. These can be mapped to tool parameters.", "add_llm_parameter": "Add LLM Parameter", "llm_filled_label": "LLM Filled", "llm_variable_mapping": "LLM Variable Mapping", "llm_variable_name": "LLM Variable Name", "select_llm_variable": "Select LLM variable to map", "no_llm_parameters": "No LLM parameters defined. Add them in the LLM Parameters tab.", "variable_name": "Variable Name", "llm_prompt": "LLM Prompt (Optional)", "llm_prompt_placeholder": "Specific instructions for the LLM to generate this parameter...", "no_llm_parameters_message": "No LLM parameters defined. Click \"Add LLM Parameter\" to create parameters that the LLM will generate.", "parameter_value": "Parameter Value", "parameter_value_placeholder": "Select LLM variable or enter custom value", "direct_input": "Direct Input", "direct_input_description": "(Enter value manually)", "enter_parameter_value": "Enter parameter value...", "enter_parameter_value_template": "Enter parameter value with template syntax. Use {{llmParam.variableName}} for LLM variables.\n\nExamples:\n- Simple: \"active\"\n- Template: \"status = '{{llmParam.orderStatus}}' AND date > '2024-01-01'\"\n- JSON: {\"status\": \"{{llmParam.status}}\", \"limit\": \"10\"}", "llm_variable_selected": "This parameter will be filled by LLM variable:", "type": "Type", "required": "Required", "llm_variable": "LLM Variable", "llm_variable.name_empty": "(Empty Name)"}}, "view": {"additional": {"created_at": "Created At", "created_by": "Created By", "desc": "Timestamps and metadata", "title": "Additional Information", "updated_at": "Updated At", "updated_by": "Updated By"}, "basic": {"created": "Created", "desc": "Datasource details and metadata", "name": "Name", "status": "Status", "title": "Basic Information", "type": "Type"}, "buttons": {"delete": "Delete", "edit": "Edit"}, "connection": {"access_key": "Access Key", "desc": "Connection information for this datasource", "no_url": "No URL", "title": "Connection Details", "url": "URL"}, "content": {"desc": "The text content stored in this datasource", "label": "Content", "no_content": "No content", "title": "Content"}, "header": {"subtitle": "View datasource details"}, "status": {"active": "Active", "inactive": "Inactive"}}, "test": {"dialog": {"title": "Test Datasource Tool", "description": "Test your tool configuration with sample parameters to verify it works correctly.", "close": "Close", "test": "Run Test", "testing": "Testing..."}, "parameters": {"title": "Tool Parameters", "description": "Enter values for the tool parameters below. Required parameters must be filled.", "no_parameters": "This tool has no parameters to configure.", "required": "Required"}, "results": {"title": "Test Results", "success": "Tool executed successfully!", "error": "Tool execution failed", "response": "Response Data"}}}