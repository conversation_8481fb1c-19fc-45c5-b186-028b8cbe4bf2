# Application URLs and Languages

## Base URL of the frontend application (used for generating links, callbacks, etc.)
NEXT_PUBLIC_APP_BASE_URL=https://yourdomain.com

## Supported languages by the app (comma-separated list)
SUPPORTED_LANGUAGES=en,id,ja,ar

## Default language of the app (used if user preference is not set)
DEFAULT_LANGUAGE=id


# WhatsApp and Messaging Provider Configuration

## WAHA API base URL for WhatsApp messaging provider
WAHA_API_URL=https://your-waha-instance.waha.my.id

## API key to authenticate WAHA API requests
WAHA_API_KEY=your_waha_api_key_here

## WhatsApp provider selection (currently only 'waha' supported)
CONVERSATION_PROVIDER=waha

## Broadcast provider selection (currently only 'waha' supported)
BROADCAST_PROVIDER=waha

## Webhook endpoint where <PERSON><PERSON> sends WhatsApp events (messages, status, etc.)
WAHA_WEBHOOK_URL=https://yourdomain.com/api/v1/functions/webhook


# Security and Internal Communication

## Internal secret token for securing communication between microservices
INTERNAL_SECRET_TOKEN=your_internal_secret_token_here


# Real-Time Messaging (Pusher) Configuration

## Pusher App ID for real-time messaging service
PUSHER_APP_ID=your_pusher_app_id_here

## Pusher Key
PUSHER_KEY=your_pusher_key_here

## Pusher Secret
PUSHER_SECRET=your_pusher_secret_here

## Pusher Cluster region (e.g. ap1)
PUSHER_CLUSTER=ap1


# Cache and Storage Configuration

## Upstash Redis REST API endpoint for caching or ephemeral data storage
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io

## Token for Upstash Redis REST API
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token_here


# Email Service Configuration

## API key for Resend email service
RESEND_API_KEY=your_resend_api_key_here

## Email domain used to send emails
EMAIL_DOMAIN_SEND=yourdomain.com


# Database Configuration

## MongoDB connection string (replace username, password, cluster, and database)
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority&appName=YourApp

## Redis Local
REDIS_URL=redis://localhost:6379

## Redis connection URL (can be Upstash)
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io

## Token for Redis authentication
UPSTASH_REDIS_REST_TOKEN=your_redis_token_here


# JWT (JSON Web Token) Secrets

## Secret key used for JWT refresh tokens
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here

## Secret key used for JWT access tokens
JWT_SECRET=your_jwt_secret_here


# Encryption Configuration

## Encryption key for securing sensitive data (OAuth tokens, etc.)
## Generate with: openssl rand -hex 32
ENCRYPTION_KEY=your_encryption_key_here

## Salt for key derivation (optional, used if ENCRYPTION_KEY is short)
ENCRYPTION_SALT=your_encryption_salt_here


# Google OAuth Configuration

## Google OAuth Client ID (from Google Cloud Console)
GOOGLE_OAUTH_CLIENT_ID=your_google_client_id_here.apps.googleusercontent.com

## Google OAuth Client Secret (from Google Cloud Console)
GOOGLE_OAUTH_CLIENT_SECRET=your_google_client_secret_here

## OAuth redirect URI (must match Google Cloud Console configuration)
GOOGLE_OAUTH_REDIRECT_URI=https://yourdomain.com/api/v1/integrations/oauth/callback


# AI Engine and Parser Configuration

## AI engine webhook URL to receive AI execution requests
AI_ENGINE_WEBHOOK_URL=https://your-n8n-instance.com/webhook/your-webhook-id

## Callback URL for AI execution results
AI_ENGINE_CALLBACK_AI_EXECUTION=https://yourdomain.com/api/v1/ai/execution

## Callback URL for AI workflow execution steps (replace [id] with workflow id)
AI_ENGINE_CALLBACK_AI_WORKFLOW_EXECUTION=https://yourdomain.com/api/v1/ai-workflow-executions/[id]/steps

## Parser engine webhook URL for processing incoming data
PARSER_ENGINE_URL=https://your-n8n-instance.com/webhook/your-parser-webhook-id

## Callback URL for parser results
PARSER_CALLBACK_URL=https://yourdomain.com/api/v1/knowledge-base/parser-callback


# Broadcast and Search Services

## Callback URL for broadcast sender service
BROADCAST_SENDER_CALLBACK_URL=https://yourdomain.com/api/v1/broadcast/send

## Pinecone API key for vector database (used for semantic search and embeddings)
PINECONE_API_KEY=your_pinecone_api_key_here


# Development Environment

## Base URL for local development chat client
CHAT_CLIENT_BASE_URL=http://localhost:5173


# Logging Configuration

## Log verbosity level (debug, info, warn, error)
LOG_LEVEL=info

## Whether to override default console.log with custom logger
OVERRIDE_CONSOLE=true

## Enable detailed stack traces in logs (useful for debugging)
ENABLE_STACK_TRACE=false


# Grafana Loki Configuration

## Grafana Loki endpoint URL for log aggregation
GRAFANA_LOKI_ENDPOINT=http://localhost:3100

## Grafana API key for authentication (optional if using username/password)
GRAFANA_API_KEY=your-grafana-api-key-here

## Grafana username for basic authentication (alternative to API key)
GRAFANA_USERNAME=your-grafana-username

## Grafana password for basic authentication (alternative to API key)
GRAFANA_PASSWORD=your-grafana-password

## Grafana datasource name (usually 'loki')
GRAFANA_DATASOURCE=loki

## Number of log entries to batch before sending to Grafana
GRAFANA_BATCH_SIZE=100

## Interval in milliseconds to flush logs to Grafana
GRAFANA_FLUSH_INTERVAL=5000

## Number of retry attempts for failed log submissions
GRAFANA_RETRY_ATTEMPTS=3

## Delay in milliseconds between retry attempts
GRAFANA_RETRY_DELAY=1000


# RabbitMQ Configuration

## RabbitMQ default username (if applicable)
RABBITMQ_DEFAULT_USER=

## RabbitMQ default password (if applicable)
RABBITMQ_DEFAULT_PASS=

## RabbitMQ default virtual host
RABBITMQ_DEFAULT_VHOST=/

## RabbitMQ node name
RABBITMQ_NODE_NAME=rabbit@cs-ai-rabbitmq

## RabbitMQ connection URL using AMQP protocol
RABBITMQ_URL=amqp://admin:admin123@localhost:5672

## RabbitMQ connection URL for broadcast worker (if different)
RABBITMQ_URL_WORKER=amqp://admin:admin123@rabbitmq-container-name:5672

## RabbitMQ Management UI URL (usually accessible in a browser)
RABBITMQ_MANAGEMENT_URL=http://localhost:15672


# Queue and Retry Settings

## Number of retry attempts for failed messages
QUEUE_RETRY_ATTEMPTS=3

## Delay (in milliseconds) between retry attempts
QUEUE_RETRY_DELAY=5000

## Message time-to-live (TTL) in milliseconds before expiration
MESSAGE_TTL=7200000

## Maximum number of retries before dropping a message
MAX_RETRIES=5

## Maximum number of messages allowed in the queue
MAX_QUEUE_LENGTH=5000


# RabbitMQ Performance Tuning

## Maximum simultaneous RabbitMQ connections allowed
MAX_CONNECTIONS=1000

## Maximum channels per RabbitMQ connection
MAX_CHANNELS=2000


# Python Worker Environment Variables

## Disable output buffering for Python worker logs
PYTHONUNBUFFERED=1

## Delay in seconds before retrying failed Python tasks
RETRY_DELAY=3


# Health Check Configuration

## Timeout (milliseconds) for service health checks
HEALTH_CHECK_TIMEOUT=5000

## Interval (milliseconds) between health checks
HEALTH_CHECK_INTERVAL=30000

## Timeout (seconds) for worker health checks
HEALTH_CHECK_TIMEOUT_WORKER=10
